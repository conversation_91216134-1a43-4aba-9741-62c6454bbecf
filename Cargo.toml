[package]
name = "gitrinth"
version = "0.1.0"
edition = "2024"
description = "A fast tool for managing .mrpack based packs through Git (and generating server packs from them)"
authors = ["StormDragon_64"]
license = "MIT"

[dependencies]
clap = { version = "4.4", features = ["derive"] }
zip = "0.6"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
git2 = "0.18"
walkdir = "2.4"
anyhow = "1.0"
dialoguer = "0.11"
tempfile = "3.8"
dirs = "5.0"
toml = "0.8"
reqwest = { version = "0.11", features = ["blocking"] }
