# Gitrinth - Minecraft Modpack Processing Tool

## Current Task: Interactive .mrpack File Selection ✅ COMPLETED

### Task Details
- **Objective**: Allow users to interactively choose between multiple .mrpack files when using smart commands
- **Problem**: Currently when multiple .mrpack files exist, commands fail and require explicit --input specification
- **Solution**: Present numbered list of .mrpack files and allow user to select (1, 2, 3, etc.)

### Implementation Status ✅ COMPLETED
- **Interactive Selection Menu**: ✅ Uses `dialoguer::Select` for user-friendly selection
- **File Display**: ✅ Shows clean filenames (without full paths) in selection menu
- **User Feedback**: ✅ Confirms selected file with checkmark and full path
- **Seamless Integration**: ✅ Works with all existing commands (extract, server-pack, full, unpack)

### What Was Implemented
1. **Enhanced `auto_detect_mrpack_file()` Function**:
   - When multiple .mrpack files found, shows interactive selection menu
   - Displays clean filenames for easy selection
   - Returns selected file path for continued processing
   - Maintains existing behavior for 0 or 1 file scenarios

2. **User Experience Improvements**:
   - Clear prompt: "Please choose which .mrpack file to use"
   - Numbered selection (arrow keys + Enter or number keys)
   - Confirmation message with selected file path
   - No breaking changes to existing functionality

### Packages Used
- `dialoguer` - Already available dependency for interactive prompts
- Existing: `std::path`, `anyhow` for error handling

### Usage Example
```bash
# When multiple .mrpack files exist in directory:
gitrinth server-pack

# Output:
# 🔍 Multiple .mrpack files found in directory:
# Please choose which .mrpack file to use:
#   My Modpack v1.2.mrpack
#   Test Pack.mrpack
#   Server Ready Pack.mrpack
#
# [User selects option 1]
# ✅ Selected: /path/to/My Modpack v1.2.mrpack
# [Command continues with selected file...]
```
- **Configuration Location**: Cross-platform standard location (using `dirs` crate)
- **Configuration Format**: TOML for human-readability
- **Configuration Contents**:
  1. Version field for handling breaking changes (v1)
  2. Server pack naming convention (default: " (Server Pack)" suffix)
  3. Files AND directories to exclude from overrides folder
  4. Default paths for extract and unpack operations
- **New Unpack Command**: Downloads mods and combines with overrides

### Current Implementation Status ✅ COMPLETED
- **Server Pack Naming**: ✅ Now configurable via `server_pack_suffix` in config.toml
- **Overrides Filtering**: ✅ Now filters both client mods AND excluded directories
- **Configuration System**: ✅ Full TOML-based config system implemented

### What Was Implemented
1. **Cross-platform Configuration**: Uses `dirs` crate to store config in standard locations
   - Windows: `%APPDATA%\gitrinth\config.toml`
   - Linux/macOS: `~/.config/gitrinth/config.toml`

2. **Configurable Server Pack Naming**:
   - Default: " (Server Pack)" suffix
   - Fully customizable via `server_pack_suffix` setting

3. **Directory Exclusion System**:
   - Default excluded directories: resourcepacks, shaderpacks, saves, figura, fancymenu_data
   - Fully customizable via `excluded_directories` array
   - Case-insensitive matching

4. **Automatic Config Creation**: Creates default config file on first run
5. **Backward Compatibility**: Existing functionality unchanged, just enhanced

### Packages Being Used
- `dirs` - For cross-platform config directory detection
- `toml` - For TOML configuration file format
- Existing: `serde`, `anyhow`, `std::fs`, `std::path`

## Core Functionality Requirements

### Primary Features
1. **Extract and Commit**: Take a .mrpack file, copy to directory, extract, commit to git repo
2. **Server Pack Generation**: Create server pack by filtering out client-side mods based on user list
3. **Flexible Operation**: Allow server-pack-only mode when no git repo is found

### Technical Details
- **Language**: Rust (for speed and efficiency)
- **File Format**: .mrpack files (Modrinth modpack format - ZIP archives)
- **Git Integration**: Automatic repo detection, user-prompted commit messages
- **Mod Filtering**: Partial name matching for jar files in both modrinth index and overrides folder

### Key Packages/Dependencies
- `clap` - Command line argument parsing
- `zip` - ZIP file handling for .mrpack files
- `serde` and `serde_json` - JSON parsing for modrinth index
- `git2` - Git operations
- `walkdir` - Directory traversal
- `anyhow` - Error handling
- `dialoguer` - Interactive prompts

### List File Format
**Filename**: `client-mods.txt` (or user-specified)
**Format**: One mod name per line, supports partial matching
```
optifine
jei
waila
```
- Case-insensitive matching
- Partial name matching (e.g., "optifine" matches "OptiFine_1.19.2_HD_U_H9.jar")
- Comments supported with `#` prefix
- Empty lines ignored

## Development Phases



## Current Implementation Status
✅ **COMPLETED** - All core functionality implemented and working!

### What's Working:
- ✅ Rust project with all dependencies
- ✅ Complete CLI interface with clap
- ✅ .mrpack file extraction and parsing
- ✅ Git repository detection, initialization, and commit functionality
- ✅ Client mod filtering with partial name matching
- ✅ Server pack generation with proper metadata preservation
- ✅ Interactive user prompts and progress indicators
- ✅ Comprehensive error handling
- ✅ Sample client-mods.txt generation
- ✅ Full documentation and usage examples

### ✨ **NEW ENHANCEMENTS COMPLETED:**
- ✅ **Auto-detection**: Automatically finds .mrpack files when only one exists in directory
- ✅ **Smart defaults**: Auto-generates output names with " (Server Pack)" suffix
- ✅ **Inline comments**: Support for comments after mod names in list files
- ✅ **User confirmation**: Shows preview of mods to be removed and asks for confirmation
- ✅ **Optional parameters**: All input/output parameters are now optional with smart defaults

### Ready for Use:
The tool is fully functional and ready for real-world use. Users can:
1. Extract .mrpack files with git integration
2. Generate server packs by filtering client-side mods
3. Use the full workflow for complete processing
4. Customize client mod lists with easy-to-understand format
5. **NEW**: Use smart defaults for effortless operation
6. **NEW**: Get confirmation before removing mods
7. **NEW**: Add inline documentation to mod lists

### Enhanced User Experience:
- Simple command: `gitrinth server-pack` (no parameters needed!)
- Clear feedback on auto-detected files and generated names
- Safety confirmation before mod removal
- Better documentation with inline comments support

### Next Steps for Enhancement:
- Real-world testing with actual modpacks
- Performance optimization for large modpacks
- Additional output formats
- GUI interface (future consideration)

## Configuration System Development Phases

### Phase 1: Core Configuration Infrastructure ✅ (COMPLETED)
- ✅ Create configuration data structures
- ✅ Implement cross-platform config file handling
- ✅ Add default configuration values
- ✅ Create config loading/saving functionality

### Phase 2: Integration with Existing Features ✅ (COMPLETED)
- ✅ Modify server pack naming to use config
- ✅ Update overrides filtering to respect excluded directories
- ✅ Ensure backward compatibility

### Phase 3: User Experience Enhancements ✅ (COMPLETED)
- ✅ Add CLI commands for config management (`gitrinth config edit/show/path/reset`)
- ✅ Provide helpful error messages for config issues
- ✅ Add config validation
- ✅ Improved editor support (uses system default for .toml files)

### Phase 4: Testing and Documentation ✅ (COMPLETED)
- ✅ Write unit tests for config functionality
- ✅ Update README with configuration documentation
- ✅ Test cross-platform behavior
- ✅ Enhanced config to support both files AND directories

## Key Files Modified ✅ COMPLETED
- ✅ `Cargo.toml` - Added `dirs` and `toml` dependencies
- ✅ `src/lib.rs` - Added config module and public exports
- ✅ `src/config.rs` - Complete configuration module with TOML support
- ✅ `src/utils.rs` - Updated `generate_server_pack_name()` to use config
- ✅ `src/filter.rs` - Enhanced filtering to support files AND directories
- ✅ `src/main.rs` - Added complete `gitrinth config` command suite
- ✅ `README.md` - Updated with comprehensive configuration documentation

## 🎉 CONFIGURATION SYSTEM COMPLETE!

### New Features Added:
1. **Cross-platform Configuration**: Automatically stored in standard locations
2. **Configurable Server Pack Naming**: Fully customizable suffix
3. **Enhanced Exclusion System**: Support for both files AND directories
4. **Complete CLI Management**: `edit`, `show`, `path`, `reset` commands
5. **Improved Editor Support**: Uses system default editor for .toml files
6. **Robust Error Handling**: Clear error messages and validation
7. **Backward Compatibility**: All existing functionality preserved

### Usage Examples:
```bash
# Manage configuration
gitrinth config edit    # Edit in default editor
gitrinth config show    # View current settings
gitrinth config reset   # Reset to defaults

# New unpack command
gitrinth unpack         # Unpack to ./unpacked (default)
gitrinth unpack -o ./my-modpack --force  # Force unpack to specific dir

# Configuration automatically used in all operations
gitrinth server-pack    # Uses configured naming and exclusions
gitrinth extract        # Uses configured extract directory
```

## 🎉 ALL REQUESTED FEATURES IMPLEMENTED!

### ✅ New Features Added:
1. **Unpack Command**: Downloads mods and combines with overrides
   - Smart directory conflict handling (cancel/delete all/selective replace)
   - Uses configurable default directory
   - Force mode for automation

2. **Enhanced Configuration System**:
   - Version field (v1) for future breaking changes
   - Support for both files AND directories in exclusions
   - Default paths for extract and unpack operations
   - Removed backward compatibility as requested

3. **Improved Editor Support**: Uses system default for .toml files
4. **Complete CLI Management**: edit, show, path, reset commands
5. **Robust Error Handling**: Version compatibility checks and validation
