---
type: "always_apply"
description: "Directions on effectively organizing important information for yourself to refer back to as you code."
---
As you code, update what you are doing in a file called "reference.md" within the main directory of the project. This should contain your current task and the specific details about the details about it (ex. specific functionality that needs to be implemented for that task, what packages you are using to accomplish it, etc.). If there is any other useful information that you want to actively reference for a task, please put it in the reference.md file. 

In another section of the reference.md file, you should also plan out phases of development. This will allow you to develop the project in organized phases so that you can focus on one thing at a time, as well as being able to program functions in an order as to not need placeholder elements, by building out the foundation before the elements that use it.