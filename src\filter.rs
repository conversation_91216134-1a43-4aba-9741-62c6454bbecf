use anyhow::{Context, Result};
use dialoguer::Confirm;
use std::collections::HashSet;
use std::fs;
use std::path::Path;
use walkdir::WalkDir;

use crate::config::GitrinthConfig;
use crate::mrpack::{ModFile, MrpackExtraction};

/// Represents a list of client-side mods to filter out
pub struct ClientModsList {
    pub mod_names: HashSet<String>,
}

impl ClientModsList {
    /// Load client mods list from a file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        let content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read client mods list file: {}", path.display()))?;

        let mut mod_names = HashSet::new();

        for line in content.lines() {
            let line = line.trim();

            // Skip empty lines and comments
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            // Handle inline comments - split on # and take the first part
            let mod_name = if let Some(hash_pos) = line.find('#') {
                line[..hash_pos].trim()
            } else {
                line
            };

            // Skip if empty after removing inline comment
            if mod_name.is_empty() {
                continue;
            }

            // Convert to lowercase for case-insensitive matching
            mod_names.insert(mod_name.to_lowercase());
        }

        Ok(ClientModsList { mod_names })
    }

    /// Check if a filename matches any of the client mod patterns
    pub fn matches_filename(&self, filename: &str) -> bool {
        let filename_lower = filename.to_lowercase();

        // Check for partial matches
        for mod_name in &self.mod_names {
            if filename_lower.contains(mod_name) {
                return true;
            }
        }

        false
    }

    /// Check if a mod file path matches any client mod patterns
    pub fn matches_mod_file(&self, mod_file: &ModFile) -> bool {
        // Extract filename from path
        if let Some(filename) = Path::new(&mod_file.path).file_name() {
            if let Some(filename_str) = filename.to_str() {
                return self.matches_filename(filename_str);
            }
        }

        false
    }
}

/// Filter an MrpackExtraction to create a server pack
pub struct ServerPackFilter<'a> {
    pub extraction: &'a mut MrpackExtraction,
    pub client_mods: &'a ClientModsList,
}

impl<'a> ServerPackFilter<'a> {
    pub fn new(extraction: &'a mut MrpackExtraction, client_mods: &'a ClientModsList) -> Self {
        Self {
            extraction,
            client_mods,
        }
    }

    /// Filter the modrinth index to remove client-side mods
    pub fn filter_index(&mut self) -> Result<Vec<String>> {
        let mut removed_mods = Vec::new();

        // Filter out client-side mods from the files list
        let original_files = self.extraction.index.files.clone();
        self.extraction.index.files.clear();

        for mod_file in original_files {
            if self.client_mods.matches_mod_file(&mod_file) {
                removed_mods.push(mod_file.path.clone());
                println!("Filtering out client mod: {}", mod_file.path);
            } else {
                self.extraction.index.files.push(mod_file);
            }
        }

        Ok(removed_mods)
    }

    /// Remove client-side mods from the overrides directory
    pub fn filter_overrides(&mut self) -> Result<Vec<String>> {
        let mut removed_files = Vec::new();

        if let Some(overrides_path) = &self.extraction.overrides_path {
            // Look for .jar files in the overrides/mods directory
            let mods_dir = overrides_path.join("mods");

            if mods_dir.exists() {
                for entry in WalkDir::new(&mods_dir) {
                    let entry = entry.with_context(|| {
                        format!("Failed to read directory entry in: {}", mods_dir.display())
                    })?;

                    let path = entry.path();

                    if path.is_file() {
                        if let Some(filename) = path.file_name() {
                            if let Some(filename_str) = filename.to_str() {
                                if filename_str.ends_with(".jar")
                                    && self.client_mods.matches_filename(filename_str)
                                {
                                    println!(
                                        "Removing client mod from overrides: {}",
                                        filename_str
                                    );
                                    fs::remove_file(path).with_context(|| {
                                        format!("Failed to remove file: {}", path.display())
                                    })?;
                                    removed_files.push(filename_str.to_string());
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(removed_files)
    }

    /// Remove excluded files and directories from the overrides folder
    pub fn filter_excluded_overrides(&mut self, config: &GitrinthConfig) -> Result<Vec<String>> {
        let mut removed_items = Vec::new();

        if let Some(overrides_path) = &self.extraction.overrides_path {
            // Check each file and directory in overrides
            if let Ok(entries) = fs::read_dir(overrides_path) {
                for entry in entries {
                    let entry = entry.with_context(|| {
                        format!(
                            "Failed to read directory entry in: {}",
                            overrides_path.display()
                        )
                    })?;

                    let path = entry.path();

                    if let Some(name) = path.file_name() {
                        if let Some(name_str) = name.to_str() {
                            if config.should_exclude_override(name_str) {
                                if path.is_dir() {
                                    println!(
                                        "Removing excluded directory from overrides: {}",
                                        name_str
                                    );
                                    fs::remove_dir_all(&path).with_context(|| {
                                        format!("Failed to remove directory: {}", path.display())
                                    })?;
                                } else {
                                    println!("Removing excluded file from overrides: {}", name_str);
                                    fs::remove_file(&path).with_context(|| {
                                        format!("Failed to remove file: {}", path.display())
                                    })?;
                                }
                                removed_items.push(name_str.to_string());
                            }
                        }
                    }
                }
            }
        }

        Ok(removed_items)
    }

    /// Apply all filters to create a server pack with user confirmation
    pub fn apply_filters(&mut self, config: &GitrinthConfig) -> Result<FilterResults> {
        // First, identify what would be removed without actually removing it
        let mods_to_remove_from_index = self.identify_mods_to_remove_from_index();
        let files_to_remove_from_overrides = self.identify_files_to_remove_from_overrides()?;
        let directories_to_remove = self.identify_directories_to_remove(config)?;

        // Show what will be removed and ask for confirmation
        self.show_removal_preview(
            &mods_to_remove_from_index,
            &files_to_remove_from_overrides,
            &directories_to_remove,
        );

        if !mods_to_remove_from_index.is_empty()
            || !files_to_remove_from_overrides.is_empty()
            || !directories_to_remove.is_empty()
        {
            let confirmed = Confirm::new()
                .with_prompt("Do you want to proceed with removing these items?")
                .default(true)
                .interact()
                .with_context(|| "Failed to get user confirmation")?;

            if !confirmed {
                println!("❌ Operation cancelled by user.");
                return Err(anyhow::anyhow!("Operation cancelled by user"));
            }
        }

        // Now actually perform the removal
        let removed_from_index = self.filter_index()?;
        let removed_from_overrides = self.filter_overrides()?;
        let removed_directories = self.filter_excluded_overrides(config)?;

        Ok(FilterResults {
            removed_from_index,
            removed_from_overrides,
            removed_excluded_overrides: removed_directories,
        })
    }

    /// Identify mods that would be removed from index without actually removing them
    fn identify_mods_to_remove_from_index(&self) -> Vec<String> {
        let mut mods_to_remove = Vec::new();

        for mod_file in &self.extraction.index.files {
            if self.client_mods.matches_mod_file(mod_file) {
                mods_to_remove.push(mod_file.path.clone());
            }
        }

        mods_to_remove
    }

    /// Identify files that would be removed from overrides without actually removing them
    fn identify_files_to_remove_from_overrides(&self) -> Result<Vec<String>> {
        let mut files_to_remove = Vec::new();

        if let Some(overrides_path) = &self.extraction.overrides_path {
            let mods_dir = overrides_path.join("mods");

            if mods_dir.exists() {
                for entry in WalkDir::new(&mods_dir) {
                    let entry = entry.with_context(|| {
                        format!("Failed to read directory entry in: {}", mods_dir.display())
                    })?;

                    let path = entry.path();

                    if path.is_file() {
                        if let Some(filename) = path.file_name() {
                            if let Some(filename_str) = filename.to_str() {
                                if filename_str.ends_with(".jar")
                                    && self.client_mods.matches_filename(filename_str)
                                {
                                    files_to_remove.push(filename_str.to_string());
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(files_to_remove)
    }

    /// Identify files and directories that would be removed from overrides without actually removing them
    fn identify_directories_to_remove(&self, config: &GitrinthConfig) -> Result<Vec<String>> {
        let mut items_to_remove = Vec::new();

        if let Some(overrides_path) = &self.extraction.overrides_path {
            // Check each file and directory in overrides
            if let Ok(entries) = fs::read_dir(overrides_path) {
                for entry in entries {
                    let entry = entry.with_context(|| {
                        format!(
                            "Failed to read directory entry in: {}",
                            overrides_path.display()
                        )
                    })?;

                    let path = entry.path();

                    if let Some(name) = path.file_name() {
                        if let Some(name_str) = name.to_str() {
                            if config.should_exclude_override(name_str) {
                                items_to_remove.push(name_str.to_string());
                            }
                        }
                    }
                }
            }
        }

        Ok(items_to_remove)
    }

    /// Show preview of what will be removed
    fn show_removal_preview(
        &self,
        mods_from_index: &[String],
        files_from_overrides: &[String],
        directories_from_overrides: &[String],
    ) {
        println!("\n📋 Removal Preview:");
        println!("==================");

        if mods_from_index.is_empty()
            && files_from_overrides.is_empty()
            && directories_from_overrides.is_empty()
        {
            println!("✅ No items found to remove.");
            return;
        }

        if !mods_from_index.is_empty() {
            println!(
                "\n🗂️  Mods to remove from modrinth index ({}):",
                mods_from_index.len()
            );
            for mod_path in mods_from_index {
                println!("   - {}", mod_path);
            }
        }

        if !files_from_overrides.is_empty() {
            println!(
                "\n📁 Files to remove from overrides ({}):",
                files_from_overrides.len()
            );
            for file in files_from_overrides {
                println!("   - {}", file);
            }
        }

        if !directories_from_overrides.is_empty() {
            println!(
                "\n📂 Excluded items to remove from overrides ({}):",
                directories_from_overrides.len()
            );
            for item in directories_from_overrides {
                println!("   - {}", item);
            }
        }

        println!();
    }
}

/// Results of the filtering operation
pub struct FilterResults {
    pub removed_from_index: Vec<String>,
    pub removed_from_overrides: Vec<String>,
    pub removed_excluded_overrides: Vec<String>,
}

impl FilterResults {
    /// Print a summary of what was filtered
    pub fn print_summary(&self) {
        println!("\n=== Filtering Summary ===");
        println!(
            "Removed {} mods from modrinth index",
            self.removed_from_index.len()
        );
        println!(
            "Removed {} files from overrides",
            self.removed_from_overrides.len()
        );
        println!(
            "Removed {} excluded items from overrides",
            self.removed_excluded_overrides.len()
        );

        if !self.removed_from_index.is_empty() {
            println!("\nMods removed from index:");
            for mod_path in &self.removed_from_index {
                println!("  - {}", mod_path);
            }
        }

        if !self.removed_from_overrides.is_empty() {
            println!("\nFiles removed from overrides:");
            for file in &self.removed_from_overrides {
                println!("  - {}", file);
            }
        }

        if !self.removed_excluded_overrides.is_empty() {
            println!("\nExcluded items removed from overrides:");
            for item in &self.removed_excluded_overrides {
                println!("  - {}", item);
            }
        }

        println!("=========================\n");
    }
}
